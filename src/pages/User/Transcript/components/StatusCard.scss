.status-card {
  background: #fff;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  margin: 0 auto;

  h5 {
    color: #333;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
  }

  &-outer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  &-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
  }

  &-description {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
  }

  // Status-specific styles
  &-completed {
    border-top: 4px solid #52c41a;
  }

  &-failed, &-analysis_failed, &-transcription_failed {
    border-top: 4px solid #f5222d;
  }

  &-queued {
    border-top: 4px solid #faad14;
  }

  &-transcription_inprogress, &-in_process, &-analysis_inprogress, &-inprocess {
    border-top: 4px solid #1890ff;
  }

  &-inactive {
    border-top: 4px solid #fa8c16;
  }

  &-unavailable, &-undefined, &-null, &-summary_unavailable, &-key_insights_unavailable {
    border-top: 4px solid #d9d9d9;
  }
}