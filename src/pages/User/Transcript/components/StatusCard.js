import React from "react";
import "./StatusCard.scss";

export default function StatusCard({ status }) {
  return (
    <div className={`status-card status-card-${status?.toLowerCase()}`}>
      <h5>Meeting Analysis</h5>
      <div className="status-card-outer">
        <span className="status-card-title">
          {status === "COMPLETED"
            ? "Completed"
            : status === "FAILED" || status === "failed"
            ? "Failed"
            : status === "analysis_failed"
            ? "Analysis Failed"
            : status === "QUEUED" || status === "queued"
            ? "Queued"
            : status === "IN_PROCESS" || status === "analysis_inprogress" || status === "inprocess"
            ? "In Progress"
            : status === "unavailable" || status === "undefined" || status === "null" || status === undefined
            ? "Not Available"
            : status === "INACTIVE"
            ? "Subscription Feature Not Available"
            : ""}
        </span>
        <span className="status-card-description">
          {status === "COMPLETED"
            ? "Your meeting has been transcribed successfully."
            : status === "QUEUED" || status === "queued"
            ? "Kindly please wait while your recording is being queued for transcription."
            : status === "FAILED" || status === "analysis_failed" || status === "failed"
            ? "Sorry for the unfortunate situation. Please raise a ticket and we will resolve it soon!"
            : status === "IN_PROCESS" || status === "analysis_inprogress" || status === "inprocess"
            ? "Your transcription is in progress, you will be able to view your transcription here very soon, please come back in some time."
            : status === "unavailable" || status === "undefined" || status === "null" || status === undefined
            ? "Something went wrong, our team is working on it. Thanks for your patience."
            : status === "INACTIVE"
            ? "Your plan is not active, please activate your plan to use this feature."
            : ""}
        </span>
      </div>
    </div>
  );
} 