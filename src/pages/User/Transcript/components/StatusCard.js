import React from "react";
import "./StatusCard.scss";

export default function StatusCard({ status, analysisType }) {
  const getStatusContent = () => {
    switch (status) {
      case "COMPLETED":
      case "completed":
        return {
          title: "Completed",
          description: "Your meeting has been transcribed successfully."
        };

      case "QUEUED":
      case "queued":
        return {
          title: "Transcription-In Queue",
          description: "Kindly please wait while your recording is being queued for transcription."
        };

      case "transcription_inprogress":
      case "IN_PROCESS":
      case "inprocess":
        return {
          title: "Transcription-In Progress",
          description: "Transcription in progress. It'll be available here soon — check back shortly!"
        };

      case "transcription_failed":
      case "FAILED":
      case "failed":
        return {
          title: "Uh-oh! Transcript Took a Day Off",
          description: "Oops! We couldn't process the transcript. Raise a ticket and we'll handle it!"
        };

      case "analysis_inprogress":
        // Different titles based on analysis type
        if (analysisType === "summary") {
          return {
            title: "Summary Analysis-In Progress",
            description: "Cooking up your summary! Insights will be ready soon — hang tight!"
          };
        } else if (analysisType === "key_insights") {
          return {
            title: "Key Insights Analysis-In Progress",
            description: "We're analyzing the recording. Insights will be ready soon. Thanks for waiting!"
          };
        } else {
          return {
            title: "Analysis-In Progress",
            description: "We're analyzing the recording. Results will be ready soon. Thanks for waiting!"
          };
        }

      case "analysis_failed":
        // Different titles based on analysis type
        if (analysisType === "summary") {
          return {
            title: "Uh-oh! Couldn't Crack The Summary",
            description: "Oops! We lost the summary on the way. Let us know and we'll fetch it!"
          };
        } else if (analysisType === "key_insights") {
          return {
            title: "Uh-oh! Couldn't find the Insights",
            description: "Something went wrong. Tap below to raise a ticket — we'll sort it out!"
          };
        } else {
          return {
            title: "Uh-oh! Analysis Failed",
            description: "Something went wrong. Tap below to raise a ticket — we'll sort it out!"
          };
        }

      case "summary_unavailable":
        return {
          title: "Summary-Unavailable",
          description: "Couldn't pull a summary — maybe the session was quick or didn't have enough to go on."
        };

      case "key_insights_unavailable":
        return {
          title: "Key Insights-Unavailable",
          description: "Insights unavailable — the recording may be too brief or lacked enough data."
        };

      case "unavailable":
      case "undefined":
      case "null":
      case undefined:
      case null:
        return {
          title: "Transcription-Unavailable",
          description: "No audio, video, or recording was found for this session. Please contact us for verification."
        };

      case "INACTIVE":
        return {
          title: "Not Available",
          description: "Insights unavailable — subscription not available."
        };

      default:
        return {
          title: "Processing",
          description: "Your meeting is being processed. Please check back soon."
        };
    }
  };

  const { title, description } = getStatusContent();

  return (
    <div className={`status-card status-card-${status?.toLowerCase()}`}>
      <h5>Meeting Analysis</h5>
      <div className="status-card-outer">
        <span className="status-card-title">
          {title}
        </span>
        <span className="status-card-description">
          {description}
        </span>
      </div>
    </div>
  );
}